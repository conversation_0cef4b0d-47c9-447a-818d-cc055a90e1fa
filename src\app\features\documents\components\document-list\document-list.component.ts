import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialog } from '@angular/material/dialog';

// Shared components
import { TableComponent } from '@shared/components/table/table.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Interfaces and types
import { ITableColumn, TableActionEvent, ActionDisplayMode } from '@core/gl-interfaces/I-table/i-table';
import { ColumnTypeEnum } from '@core/gl-interfaces/I-table/column-type/column-type-enum';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// Services
import { DocumentManagementService, DocumentListRequest, DocumentMetadata } from '../../services/document-management.service';

// Components
import { DocumentViewerComponent } from '../document-viewer/document-viewer.component';

@Component({
  selector: 'app-document-list',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    TableComponent,
    CustomButtonComponent
  ],
  templateUrl: './document-list.component.html',
  styleUrls: ['./document-list.component.scss']
})
export class DocumentListComponent implements OnInit {
  @Input() category: any;
  @Input() fundId: number | null = null;

  // Table configuration
  documents: DocumentMetadata[] = [];
  tableDataSource = new MatTableDataSource<DocumentMetadata>([]);
  isLoading = false;
  totalCount = 0;
  pageSize = 10;
  currentPage = 1;

  // Table columns
  tableColumns: ITableColumn[] = [
    {
      columnDef: 'fileName',
      header: 'DOCUMENTS.COLUMNS.NAME',
      columnType: ColumnTypeEnum.Text,
      cell: (element: any) => element.fileName || ''
    },
    {
      columnDef: 'fileSize',
      header: 'DOCUMENTS.COLUMNS.SIZE',
      columnType: ColumnTypeEnum.Text,
      cell: (element: any) => this.formatFileSize(element.fileSize || 0)
    },
    {
      columnDef: 'uploadDate',
      header: 'DOCUMENTS.COLUMNS.UPLOAD_DATE',
      columnType: ColumnTypeEnum.Text,
      cell: (element: any) => element.uploadDate ? new Date(element.uploadDate).toLocaleDateString() : ''
    },
    {
      columnDef: 'actions',
      header: 'DOCUMENTS.COLUMNS.ACTIONS',
      columnType: ColumnTypeEnum.Actions,
      displayMode: ActionDisplayMode.Flex,
      cell: (element: DocumentMetadata) => ({
        buttons: [
          {
            label: 'DOCUMENTS.VIEW',
            action: 'view',
            iconSrc: 'assets/icons/view.png',
            class: 'btn-primary mx-1'
          },
          {
            label: 'DOCUMENTS.DELETE',
            action: 'delete',
            iconSrc: 'assets/icons/delete.png',
            class: 'btn-danger mx-1'
          }
        ]
      })
    }
  ];

  displayedColumns = ['fileName', 'fileSize', 'uploadDate', 'actions'];

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  constructor(
    private documentService: DocumentManagementService,
    private dialog: MatDialog,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.loadDocuments();
  }

  loadDocuments(): void {
    if (!this.category) return;

    this.isLoading = true;

    const request: DocumentListRequest = {
      category: this.category,
      fundId: this.fundId || undefined,
      pageNumber: this.currentPage,
      pageSize: this.pageSize
    };

    this.documentService.getDocuments(request).subscribe({
      next: (documents) => {
        this.documents = documents;
        this.tableDataSource.data = documents;
        this.totalCount = documents.length;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading documents:', error);
        this.isLoading = false;
      }
    });
  }

  onTableAction(event: TableActionEvent): void {
    switch (event.action) {
      case 'view':
        this.viewDocument(event.row);
        break;
      case 'delete':
        this.deleteDocument(event.row);
        break;
    }
  }

  viewDocument(document: DocumentMetadata): void {
    const dialogRef = this.dialog.open(DocumentViewerComponent, {
      width: '90vw',
      height: '90vh',
      maxWidth: '1200px',
      maxHeight: '800px',
      data: {
        document: document,
        bucketName: document.bucketName
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      // Handle any actions after viewer is closed
      console.log('Document viewer closed');
    });
  }

  deleteDocument(document: DocumentMetadata): void {
    Swal.fire({
      title: this.translateService.instant('COMMON.CONFIRM_DELETE'),
      text: this.translateService.instant('DOCUMENTS.DELETE_CONFIRM', { fileName: document.fileName }),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: this.translateService.instant('COMMON.DELETE'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL')
    }).then((result) => {
      if (result.isConfirmed) {
        this.documentService.deleteDocument(document.id, document.bucketName).subscribe({
          next: (response) => {
            // Show success message
            Swal.fire({
              title: this.translateService.instant('COMMON.SUCCESS'),
              text: this.translateService.instant('DOCUMENTS.DELETE_SUCCESS'),
              icon: 'success',
              timer: 2000,
              showConfirmButton: false
            });
            // Reload the list
            this.loadDocuments();
          },
          error: (error) => {
            console.error('Error deleting document:', error);
            // Show error message
            Swal.fire({
              title: this.translateService.instant('COMMON.ERROR'),
              text: this.translateService.instant('DOCUMENTS.DELETE_FAILED'),
              icon: 'error',
              confirmButtonText: this.translateService.instant('COMMON.OK')
            });
          }
        });
      }
    });
  }

  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  onPageChange(event: any): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadDocuments();
  }
}
