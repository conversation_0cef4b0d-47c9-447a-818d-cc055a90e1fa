import { Component, Input, Output, EventEmitter, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule } from '@angular/forms';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

// Shared components
import { FileUploadComponent } from '@shared/components/file-upload/file-upload.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// API and services
import { DocumentServiceProxy, MinIOFileServiceProxy } from '@core/api/api.generated';
import { environment } from 'src/environments/environment';
import { ErrorModalService } from '@core/services/error-modal.service';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { HttpClient } from '@angular/common/http';

// Interfaces
export interface DocumentCategory {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
}

export interface DocumentUploadRequest {
  file: File;
  categoryId: number;
  fundId?: number;
  description?: string;
}

export interface DocumentUploadDialogData {
  fundId?: number;
  selectedCategory?: DocumentCategory;
}

@Component({
  selector: 'app-document-upload',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    FormsModule,
    FileUploadComponent,
    CustomButtonComponent
  ],
  templateUrl: './document-upload.component.html',
  styleUrls: ['./document-upload.component.scss']
})
export class DocumentUploadComponent {
  @Output() uploadComplete = new EventEmitter<any>();

  // Form data
  selectedCategory: number | null = null;
  documentTitle = '';
  selectedFiles: File[] = [];
  isUploading = false;

  // Document categories
  documentCategories: DocumentCategory[] = [];

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  // API URLs
  private baseUrl = environment.apiUrl;

  constructor(
    private http: HttpClient,
    private documentProxy: DocumentServiceProxy,
    private minioProxy: MinIOFileServiceProxy,
    private errorModalService: ErrorModalService,
    private dialogRef: MatDialogRef<DocumentUploadComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DocumentUploadDialogData
  ) {
    this.loadCategories();

    // Pre-select category if provided
    if (this.data?.selectedCategory) {
      this.selectedCategory = this.data.selectedCategory.id;
    }
  }

  private loadCategories(): void {
    const url = `${this.baseUrl}/api/Document/categories`;

    this.http.get<any>(url).subscribe({
      next: (response: any) => {
        // Transform API response to DocumentCategory[]
        if (response?.data && Array.isArray(response.data)) {
          this.documentCategories = response.data.map((item: any) => ({
            id: item.id,
            name: item.name,
            description: item.description,
            isActive: item.isActive ?? true
          }));
        } else {
          this.documentCategories = [];
        }

        // If no categories available, show error
        if (this.documentCategories.length === 0) {
          console.warn('No document categories available for upload');
        }
      },
      error: (error: any) => {
        console.error('Error loading categories:', error);
        this.errorModalService.showError('Failed to load document categories');
        this.documentCategories = [];
      }
    });
  }

  onFileSelected(files: File | File[] | null): void {
    if (!files) {
      this.selectedFiles = [];
      return;
    }

    if (Array.isArray(files)) {
      this.selectedFiles = files;
    } else {
      this.selectedFiles = [files];
    }
  }

  onUpload(): void {
    if (!this.selectedCategory || this.selectedFiles.length === 0) {
      return;
    }

    const category = this.documentCategories.find(cat => cat.id === this.selectedCategory);
    if (!category) {
      return;
    }

    this.isUploading = true;

    // Create upload requests for each file
    const uploadRequests: DocumentUploadRequest[] = this.selectedFiles.map(file => ({
      file: file,
      categoryId: category.id,
      fundId: this.data?.fundId,
      description: this.documentTitle
    }));

    // Upload multiple documents
    this.uploadMultipleDocuments(uploadRequests).subscribe({
      next: (results: any) => {
        this.isUploading = false;
        this.uploadComplete.emit({
          category: this.selectedCategory,
          files: this.selectedFiles,
          title: this.documentTitle,
          results: results
        });
        this.dialogRef.close(true);
      },
      error: (error: any) => {
        console.error('Upload error:', error);
        this.errorModalService.showError('Failed to upload documents');
        this.isUploading = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  isFormValid(): boolean {
    return !!(this.selectedCategory && this.selectedFiles.length > 0);
  }

  /**
   * Upload multiple documents
   * @param requests DocumentUploadRequest[]
   * @returns Observable<any>
   */
  private uploadMultipleDocuments(requests: DocumentUploadRequest[]): Observable<any> {
    const uploadPromises = requests.map(request =>
      this.uploadSingleDocument(request).toPromise()
    );

    return new Observable(observer => {
      Promise.all(uploadPromises)
        .then(results => {
          observer.next(results);
          observer.complete();
        })
        .catch(error => {
          observer.error(error);
        });
    });
  }

  /**
   * Upload single document
   * @param request DocumentUploadRequest
   * @returns Observable<any>
   */
  private uploadSingleDocument(request: DocumentUploadRequest): Observable<any> {
    const formData = new FormData();
    formData.append('file', request.file);
    formData.append('documentCategoryId', request.categoryId.toString());

    if (request.fundId) {
      formData.append('fundId', request.fundId.toString());
    }
    if (request.description) {
      formData.append('description', request.description);
    }

    const url = `${this.baseUrl}/api/Document/upload`;

    return this.http.post<any>(url, formData).pipe(
      map((response: any) => {
        return {
          success: true,
          data: response?.data,
          message: 'Document uploaded successfully'
        };
      }),
      catchError((error: any) => {
        console.error('Upload error:', error);
        this.errorModalService.showError('Failed to upload document');
        throw error;
      })
    );
  }
}
