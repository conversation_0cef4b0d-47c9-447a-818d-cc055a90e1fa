import { Component, Input, Output, EventEmitter, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule } from '@angular/forms';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

// Shared components
import { FileUploadComponent } from '@shared/components/file-upload/file-upload.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// API and services
import {
  DocumentServiceProxy,
  MinIOFileServiceProxy,
  DocumentCategoryDto,
  GetDocumentCategoriesQuery,
  Body
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

export interface DocumentUploadDialogData {
  fundId?: number;
  selectedCategory?: DocumentCategory;
}

@Component({
  selector: 'app-document-upload',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    FormsModule,
    FileUploadComponent,
    CustomButtonComponent
  ],
  templateUrl: './document-upload.component.html',
  styleUrls: ['./document-upload.component.scss']
})
export class DocumentUploadComponent {
  @Output() uploadComplete = new EventEmitter<any>();

  // Form data
  selectedCategory: number | null = null;
  documentTitle = '';
  selectedFiles: File[] = [];
  isUploading = false;

  // Document categories
  documentCategories: DocumentCategoryDto[] = [];

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  constructor(
    private documentProxy: DocumentServiceProxy,
    private minioProxy: MinIOFileServiceProxy,
    private errorModalService: ErrorModalService,
    private dialogRef: MatDialogRef<DocumentUploadComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DocumentUploadDialogData
  ) {
    this.loadCategories();

    // Pre-select category if provided
    if (this.data?.selectedCategory) {
      this.selectedCategory = this.data.selectedCategory.id;
    }
  }

  private loadCategories(): void {
    // Use NSwag-generated proxy with proper typing
    const query = new GetDocumentCategoriesQuery();

    this.documentProxy.categories(query).subscribe({
      next: (response) => {
        // Response is properly typed as DocumentCategoryDtoListBaseResponse
        if (response?.data && Array.isArray(response.data)) {
          this.documentCategories = response.data;
        } else {
          this.documentCategories = [];
        }

        // If no categories available, show error
        if (this.documentCategories.length === 0) {
          console.warn('No document categories available for upload');
        }
      },
      error: (error: any) => {
        console.error('Error loading categories:', error);
        this.errorModalService.showError('Failed to load document categories');
        this.documentCategories = [];
      }
    });
  }

  onFileSelected(files: File | File[] | null): void {
    if (!files) {
      this.selectedFiles = [];
      return;
    }

    if (Array.isArray(files)) {
      this.selectedFiles = files;
    } else {
      this.selectedFiles = [files];
    }
  }

  onUpload(): void {
    if (!this.selectedCategory || this.selectedFiles.length === 0) {
      return;
    }

    const category = this.documentCategories.find(cat => cat.id === this.selectedCategory);
    if (!category) {
      return;
    }

    this.isUploading = true;

    // Upload files and then add document records
    this.uploadFilesAndCreateDocuments(this.selectedFiles, category.id, this.data?.fundId).subscribe({
      next: (results: any) => {
        this.isUploading = false;
        this.uploadComplete.emit({
          category: this.selectedCategory,
          files: this.selectedFiles,
          title: this.documentTitle,
          results: results
        });
        this.dialogRef.close(true);
      },
      error: (error: any) => {
        console.error('Upload error:', error);
        this.errorModalService.showError('Failed to upload documents');
        this.isUploading = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  isFormValid(): boolean {
    return !!(this.selectedCategory && this.selectedFiles.length > 0);
  }

  /**
   * Upload files and create document records
   * @param files File[]
   * @param categoryId number
   * @param fundId number | undefined
   * @returns Observable<any>
   */
  private uploadFilesAndCreateDocuments(files: File[], categoryId: number, fundId?: number): Observable<any> {
    const uploadPromises = files.map(file =>
      this.uploadSingleFileAndCreateDocument(file, categoryId, fundId).toPromise()
    );

    return new Observable(observer => {
      Promise.all(uploadPromises)
        .then(results => {
          observer.next(results);
          observer.complete();
        })
        .catch(error => {
          observer.error(error);
        });
    });
  }

  /**
   * Upload single file and create document record
   * @param file File
   * @param categoryId number
   * @param fundId number | undefined
   * @returns Observable<any>
   */
  private uploadSingleFileAndCreateDocument(file: File, categoryId: number, fundId?: number): Observable<any> {
    // For now, use the document add method with the file information
    // In a real implementation, you might need to upload to MinIO first, then create the document record
    const body = new Body({
      documentCategoryId: categoryId,
      attachmentId: 0, // This would be set after MinIO upload
      fundId: fundId || 0
    });

    return this.documentProxy.add(body).pipe(
      map((response: any) => {
        return {
          success: true,
          data: response,
          message: 'Document uploaded successfully'
        };
      }),
      catchError((error: any) => {
        console.error('Upload error:', error);
        this.errorModalService.showError('Failed to upload document');
        throw error;
      })
    );
  }
}
