import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError, shareReplay } from 'rxjs/operators';

// Environment
import { environment } from 'src/environments/environment';

// Core services
import { ErrorModalService } from '@core/services/error-modal.service';

// Interfaces and types
export interface DocumentCategory {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
}

export interface DocumentMetadata {
  id: number;
  fileName: string;
  fileSize: number;
  uploadDate: Date;
  categoryId: number;
  categoryName: string;
  fundId?: number;
  bucketName: string;
  attachmentId: number;
  uploadedBy: string;
  mimeType?: string;
}

export interface DocumentListRequest {
  category?: DocumentCategory;
  fundId?: number;
  pageNumber: number;
  pageSize: number;
  search?: string;
  orderBy?: string;
}

export interface DocumentUploadRequest {
  file: File;
  categoryId: number;
  fundId?: number;
  description?: string;
}

export interface PaginatedDocumentResponse {
  data: DocumentMetadata[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

@Injectable({
  providedIn: 'root'
})
export class DocumentManagementService {
  private baseUrl = environment.apiUrl;

  constructor(
    private http: HttpClient,
    private errorModalService: ErrorModalService
  ) {}

  /**
   * Get document categories
   * @returns Observable<DocumentCategory[]>
   */
  getCategories(): Observable<DocumentCategory[]> {
    const url = `${this.baseUrl}/api/Document/categories`;

    return this.http.get<any>(url).pipe(
      map((response: any) => {
        // Transform API response to DocumentCategory[]
        if (response?.data && Array.isArray(response.data)) {
          return response.data.map((item: any) => ({
            id: item.id,
            name: item.name,
            description: item.description,
            isActive: item.isActive ?? true
          }));
        }
        return [];
      }),
      catchError(this.handleError.bind(this)),
      shareReplay(1)
    );
  }

  /**
   * Get documents list with pagination
   * @param request DocumentListRequest
   * @returns Observable<DocumentMetadata[]>
   */
  getDocuments(request: DocumentListRequest): Observable<DocumentMetadata[]> {
    let params = new HttpParams();

    if (request.category?.id) {
      params = params.set('CategoryId', request.category.id.toString());
    }
    if (request.fundId) {
      params = params.set('FundId', request.fundId.toString());
    }
    if (request.pageNumber) {
      params = params.set('PageNumber', request.pageNumber.toString());
    }
    if (request.pageSize) {
      params = params.set('PageSize', request.pageSize.toString());
    }
    if (request.search) {
      params = params.set('Search', request.search);
    }
    if (request.orderBy) {
      params = params.set('OrderBy', request.orderBy);
    }

    const url = `${this.baseUrl}/api/Document`;

    return this.http.get<any>(url, { params }).pipe(
      map((response: any) => {
        // Transform API response to DocumentMetadata[]
        if (response?.data && Array.isArray(response.data)) {
          return response.data.map((item: any) => ({
            id: item.id,
            fileName: item.fileName,
            fileSize: item.fileSize || 0,
            uploadDate: new Date(item.uploadDate || item.createdDate),
            categoryId: item.categoryId,
            categoryName: item.categoryName || '',
            fundId: item.fundId,
            bucketName: item.bucketName || 'documents',
            attachmentId: item.attachmentId,
            uploadedBy: item.uploadedBy || '',
            mimeType: item.mimeType
          }));
        }
        return [];
      }),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * Upload document
   * @param request DocumentUploadRequest
   * @returns Observable<any>
   */
  uploadDocument(request: DocumentUploadRequest): Observable<any> {
    const formData = new FormData();
    formData.append('file', request.file);
    formData.append('documentCategoryId', request.categoryId.toString());

    if (request.fundId) {
      formData.append('fundId', request.fundId.toString());
    }
    if (request.description) {
      formData.append('description', request.description);
    }

    const url = `${this.baseUrl}/api/Document/upload`;

    return this.http.post<any>(url, formData).pipe(
      map((response: any) => {
        return {
          success: true,
          data: response?.data,
          message: 'Document uploaded successfully'
        };
      }),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * Delete document
   * @param documentId number
   * @param bucketName string
   * @returns Observable<any>
   */
  deleteDocument(documentId: number, bucketName: string): Observable<any> {
    const url = `${this.baseUrl}/api/Document/${documentId}`;

    return this.http.delete<any>(url).pipe(
      map((response: any) => {
        return {
          success: true,
          message: 'Document deleted successfully'
        };
      }),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * Get document preview URL
   * @param documentId number
   * @param bucketName string
   * @param expiryInMinutes number
   * @returns Observable<any>
   */
  getDocumentPreview(documentId: number, bucketName: string, expiryInMinutes: number = 60): Observable<any> {
    const body = {
      id: documentId,
      bucketName: bucketName,
      expiryInMinutes: expiryInMinutes
    };

    const url = `${this.baseUrl}/api/MinIO/MinIOFile/Preview`;

    return this.http.post<any>(url, body).pipe(
      map((response: any) => {
        return response;
      }),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * Download document
   * @param documentId number
   * @param bucketName string
   * @returns Observable<any>
   */
  downloadDocument(documentId: number, bucketName: string): Observable<any> {
    let params = new HttpParams();
    if (bucketName) {
      params = params.set('bucketName', bucketName);
    }

    const url = `${this.baseUrl}/api/MinIO/MinIOFile/DownloadFile/${documentId}`;

    return this.http.get<any>(url, { params }).pipe(
      map((response: any) => {
        return response;
      }),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * Upload multiple documents
   * @param requests DocumentUploadRequest[]
   * @returns Observable<any>
   */
  uploadMultipleDocuments(requests: DocumentUploadRequest[]): Observable<any> {
    const uploadPromises = requests.map(request =>
      this.uploadDocument(request).toPromise()
    );

    return new Observable(observer => {
      Promise.all(uploadPromises)
        .then(results => {
          observer.next(results);
          observer.complete();
        })
        .catch(error => {
          observer.error(error);
        });
    });
  }

  /**
   * Format file size for display
   * @param bytes number
   * @returns string
   */
  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Centralized error handling
   * @param error any
   * @returns Observable<never>
   */
  private handleError(error: any): Observable<never> {
    console.error('DocumentManagementService Error:', error);
    
    let errorMessage = 'An error occurred while processing your request.';
    
    if (error?.error?.message) {
      errorMessage = error.error.message;
    } else if (error?.message) {
      errorMessage = error.message;
    }

    // Show error modal to user
    this.errorModalService.showError(errorMessage);
    
    return throwError(() => error);
  }
}
