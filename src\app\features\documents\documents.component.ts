import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';

// Core imports
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// Feature components
import { DocumentListComponent } from './components/document-list/document-list.component';
import { DocumentUploadComponent } from './components/document-upload/document-upload.component';

// Services
import { DocumentManagementService } from './services/document-management.service';
import { TokenService } from '../auth/services/token.service';

@Component({
  selector: 'app-documents',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatCardModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    DocumentListComponent,
    DocumentUploadComponent
  ],
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.scss']
})
export class DocumentsComponent implements OnInit {
  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [];
  breadcrumbSizeEnum = SizeEnum;

  // Component state
  isLoading = false;
  currentFundId: number | null = null;
  selectedTabIndex = 0;

  // Document categories (loaded from API)
  documentCategories: any[] = [];

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private translateService: TranslateService,
    private documentService: DocumentManagementService,
    public tokenService: TokenService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.loadRouteParams();
    this.loadDocumentCategories();
  }

  private initializeBreadcrumb(): void {
    this.breadcrumbItems = [
      {
        label: this.translateService.instant('COMMON.HOME'),
        url: '/admin/dashboard'
      },
      {
        label: this.translateService.instant('DOCUMENTS.TITLE'),
        url: '/admin/documents'
      }
    ];
  }

  private loadRouteParams(): void {
    this.route.params.subscribe(params => {
      if (params['fundId']) {
        this.currentFundId = +params['fundId'];
        this.updateBreadcrumbWithFund();
      }
    });
  }

  private loadDocumentCategories(): void {
    this.isLoading = true;
    this.documentService.getCategories().subscribe({
      next: (categories: any) => {
        this.documentCategories = categories;
        this.isLoading = false;

        // If no categories are returned, show appropriate message
        if (categories.length === 0) {
          console.warn('No document categories returned from API');
        }
      },
      error: (error: any) => {
        console.error('Error loading document categories:', error);
        this.documentCategories = [];
        this.isLoading = false;
      }
    });
  }

  private updateBreadcrumbWithFund(): void {
    if (this.currentFundId) {
      this.breadcrumbItems = [
        {
          label: this.translateService.instant('COMMON.HOME'),
          url: '/admin/dashboard'
        },
        {
          label: this.translateService.instant('INVESTMENT_FUNDS.TITLE'),
          url: '/admin/investment-funds'
        },
        {
          label: this.translateService.instant('FUND_DETAILS.TITLE'),
          url: `/admin/investment-funds/fund-details?id=${this.currentFundId}`
        },
        {
          label: this.translateService.instant('DOCUMENTS.TITLE'),
          url: `/admin/documents/fund/${this.currentFundId}`,
          disabled: true
        }
      ];
    }
  }

  onTabChange(index: number): void {
    this.selectedTabIndex = index;
  }

  onUploadDocument(): void {
    const dialogRef = this.dialog.open(DocumentUploadComponent, {
      width: '600px',
      data: {
        fundId: this.currentFundId,
        selectedCategory: this.getCurrentCategory()
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Refresh the current tab's document list
        this.refreshCurrentTab();
      }
    });
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url) {
      this.router.navigate([item.url]);
    }
  }

  getCurrentCategory(): any {
    return this.documentCategories[this.selectedTabIndex];
  }

  canUploadDocuments(): boolean {
    // Check if user has permission to upload documents
    return this.tokenService.hasPermission('Document.Create') ||
           this.tokenService.hasRole('FundManager') ||
           this.tokenService.hasRole('LegalCouncil');
  }

  private refreshCurrentTab(): void {
    // This will trigger a refresh of the current document list
    // The document-list component will handle the actual refresh
    console.log('Refreshing current tab documents');
  }
}
