import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

// Shared components
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// API and services
import { DocumentServiceProxy, MinIOFileServiceProxy } from '@core/api/api.generated';
import { environment } from 'src/environments/environment';
import { ErrorModalService } from '@core/services/error-modal.service';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

export interface DocumentViewerData {
  document: any;
  bucketName: string;
}

@Component({
  selector: 'app-document-viewer',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    CustomButtonComponent
  ],
  templateUrl: './document-viewer.component.html',
  styleUrls: ['./document-viewer.component.scss']
})
export class DocumentViewerComponent implements OnInit {
  isLoading = true;
  previewUrl: SafeResourceUrl | null = null;
  errorMessage = '';
  
  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  // API URLs
  private baseUrl = environment.apiUrl;

  constructor(
    private dialogRef: MatDialogRef<DocumentViewerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DocumentViewerData,
    private http: HttpClient,
    private documentProxy: DocumentServiceProxy,
    private minioProxy: MinIOFileServiceProxy,
    private errorModalService: ErrorModalService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.loadDocumentPreview();
  }

  loadDocumentPreview(): void {
    if (!this.data.document || !this.data.bucketName) {
      this.errorMessage = 'DOCUMENTS.PREVIEW_ERROR';
      this.isLoading = false;
      return;
    }

    // Use direct HTTP call for document preview
    const body = {
      id: this.data.document.id,
      bucketName: this.data.bucketName,
      expiryInMinutes: 60
    };

    const url = `${this.baseUrl}/api/MinIO/MinIOFile/Preview`;

    this.http.post<any>(url, body).subscribe({
      next: (response: any) => {
        if (response?.data?.url) {
          this.previewUrl = this.sanitizer.bypassSecurityTrustResourceUrl(response.data.url);
        } else {
          this.errorMessage = 'DOCUMENTS.PREVIEW_FAILED';
        }
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Preview error:', error);
        this.errorMessage = 'DOCUMENTS.PREVIEW_FAILED';
        this.errorModalService.showError('Failed to load document preview');
        this.isLoading = false;
      }
    });
  }

  onDownload(): void {
    const url = `${this.baseUrl}/api/MinIO/MinIOFile/DownloadFile/${this.data.document.id}`;
    let params = new HttpParams();
    if (this.data.bucketName) {
      params = params.set('bucketName', this.data.bucketName);
    }

    this.http.get(url, { params, responseType: 'blob' }).subscribe({
      next: (blob: Blob) => {
        // Create download link
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = this.data.document.fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
      },
      error: (error: any) => {
        console.error('Download error:', error);
        this.errorModalService.showError('Failed to download document');
      }
    });
  }

  onClose(): void {
    this.dialogRef.close();
  }

  getFileExtension(): string {
    if (!this.data.document?.fileName) return '';
    return this.data.document.fileName.split('.').pop()?.toLowerCase() || '';
  }

  isPreviewSupported(): boolean {
    const extension = this.getFileExtension();
    return ['pdf', 'jpg', 'jpeg', 'png', 'gif'].includes(extension);
  }

  getFileIcon(): string {
    const extension = this.getFileExtension();

    switch (extension) {
      case 'pdf':
        return 'picture_as_pdf';
      case 'doc':
      case 'docx':
        return 'description';
      case 'xls':
      case 'xlsx':
        return 'table_chart';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'image';
      default:
        return 'insert_drive_file';
    }
  }

  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
