import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

// Shared components
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Services
import { DocumentManagementService } from '../../services/document-management.service';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

export interface DocumentViewerData {
  document: any;
  bucketName: string;
}

@Component({
  selector: 'app-document-viewer',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    CustomButtonComponent
  ],
  templateUrl: './document-viewer.component.html',
  styleUrls: ['./document-viewer.component.scss']
})
export class DocumentViewerComponent implements OnInit {
  isLoading = true;
  previewUrl: SafeResourceUrl | null = null;
  errorMessage = '';
  
  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  constructor(
    private dialogRef: MatDialogRef<DocumentViewerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DocumentViewerData,
    private documentService: DocumentManagementService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.loadDocumentPreview();
  }

  loadDocumentPreview(): void {
    if (!this.data.document || !this.data.bucketName) {
      this.errorMessage = 'DOCUMENTS.PREVIEW_ERROR';
      this.isLoading = false;
      return;
    }

    this.documentService.getDocumentPreview(this.data.document.id, this.data.bucketName)
      .subscribe({
        next: (previewBlob: any) => {
          // Create object URL from blob response
          const url = URL.createObjectURL(previewBlob);
          this.previewUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
          this.isLoading = false;
        },
        error: (error: any) => {
          console.error('Preview error:', error);
          this.errorMessage = 'DOCUMENTS.PREVIEW_FAILED';
          this.isLoading = false;
        }
      });
  }

  onDownload(): void {
    this.documentService.downloadDocument(this.data.document.id, this.data.bucketName)
      .subscribe({
        next: () => {
          // Download handled by service
        },
        error: (error) => {
          console.error('Download error:', error);
        }
      });
  }

  onClose(): void {
    this.dialogRef.close();
  }

  getFileExtension(): string {
    if (!this.data.document?.fileName) return '';
    return this.data.document.fileName.split('.').pop()?.toLowerCase() || '';
  }

  isPreviewSupported(): boolean {
    const extension = this.getFileExtension();
    return ['pdf', 'jpg', 'jpeg', 'png', 'gif'].includes(extension);
  }

  getFileIcon(): string {
    const extension = this.getFileExtension();

    switch (extension) {
      case 'pdf':
        return 'picture_as_pdf';
      case 'doc':
      case 'docx':
        return 'description';
      case 'xls':
      case 'xlsx':
        return 'table_chart';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'image';
      default:
        return 'insert_drive_file';
    }
  }

  formatFileSize(bytes: number): string {
    return this.documentService.formatFileSize(bytes);
  }
}
